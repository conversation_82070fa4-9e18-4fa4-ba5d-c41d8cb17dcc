using AutoMapper.QueryableExtensions;
using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Shared;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;
using ProjectLoki.Api.Files.Data;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingFind;

public sealed class ShareholdingFindEndpoint(FlokiDbContext context, AutoMapper.IMapper mapper) : EndpointWithoutRequest<IResult>
{
    public override void Configure()
    {
        Get(AppRoutes.Shareholding.IdPath);
        Policies(AppRoutes.Shareholding.FullPath.ToGetPermission());
    }

    public override async Task<IResult> ExecuteAsync(CancellationToken ct)
    {
        var id = Route<string>(AppRoutes.IdValue);
        if (string.IsNullOrWhiteSpace(id) || !ShareholdingSubmissionId.TryParse(id, out var shareholdingSubmissionId))
        {
            return ShareholdingSubmissionErrors.ShareholdingSubmissionIdMustBeValid.Problem();
        }

        var result = await context.ShareholdingSubmissions
            .Where(x => x.Id == shareholdingSubmissionId)
            .ProjectTo<ShareholdingSubmissionMineResponse>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(ct);

        if (result is null) return Results.NotFound();

        var referenceIds = Array.Empty<string>();

        if (result.BankReportDocument?.Id != null) referenceIds = [.. referenceIds, result.BankReportDocument.Id,];
        if (result.ShareSubscriptionFormsDocument?.Id != null) referenceIds = [.. referenceIds, result.ShareSubscriptionFormsDocument.Id,];
        if (result.FeasibilityStudyDocument.Id != null) referenceIds = [.. referenceIds, result.FeasibilityStudyDocument.Id,];
        if (result.SectoralAuthorityApprovalDocument?.Id != null) referenceIds = [.. referenceIds, result.SectoralAuthorityApprovalDocument.Id,];
        if (result.FoundersCommitteeElectionDocument.Id != null) referenceIds = [.. referenceIds, result.FoundersCommitteeElectionDocument.Id,];
        if (result.SubscriptionStatementDocument?.Id != null) referenceIds = [.. referenceIds, result.SubscriptionStatementDocument.Id,];
        if (result.SubscriptionStatementPublicationDocument?.Id != null) referenceIds = [.. referenceIds, result.SubscriptionStatementPublicationDocument.Id,];
        if (result.CloseSubscriptionStatementDocument?.Id != null) referenceIds = [.. referenceIds, result.CloseSubscriptionStatementDocument.Id,];

        if (referenceIds.Length == 0) return Results.Ok(result);

        var jsonFilters = referenceIds.Select(UploadedFile.BuildRefIdJson)
            .ToArray();

        var files = await context.UploadedFiles
            .Where(f => f.References != null && jsonFilters.Any(json => EF.Functions.JsonContains(f.References!, json)))
            .ToListAsync(ct);

        if (files.Count == 0) return Results.Ok(result);
        if (result.BankReportDocument != null)
            result.BankReportDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.BankReportDocument.Id))
                .Select(f => f.Id.ToString())
                .ToArray();
        if (result.ShareSubscriptionFormsDocument != null)
            result.ShareSubscriptionFormsDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.ShareSubscriptionFormsDocument.Id))
                .Select(f => f.Id.ToString())
                .ToArray();
        result.FeasibilityStudyDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.FeasibilityStudyDocument.Id))
            .Select(f => f.Id.ToString())
            .ToArray();
        if (result.SectoralAuthorityApprovalDocument != null)
            result.SectoralAuthorityApprovalDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.SectoralAuthorityApprovalDocument.Id))
                .Select(f => f.Id.ToString())
                .ToArray();
        result.FoundersCommitteeElectionDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.FoundersCommitteeElectionDocument.Id))
            .Select(f => f.Id.ToString())
            .ToArray();
        if (result.SubscriptionStatementDocument != null)
            result.SubscriptionStatementDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.SubscriptionStatementDocument.Id))
                .Select(f => f.Id.ToString())
                .ToArray();
        if (result.SubscriptionStatementPublicationDocument != null)
            result.SubscriptionStatementPublicationDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.SubscriptionStatementPublicationDocument.Id))
                .Select(f => f.Id.ToString())
                .ToArray();
        if (result.CloseSubscriptionStatementDocument != null)
            result.CloseSubscriptionStatementDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.CloseSubscriptionStatementDocument.Id))
                .Select(f => f.Id.ToString())
                .ToArray();
        if (result.SectoralAuthorityApprovalDocument != null)
            result.SectoralAuthorityApprovalDocument.UploadedFileIds = files.Where(f => f.References!.Any(r => r.ReferenceId == result.SectoralAuthorityApprovalDocument.Id))
                .Select(f => f.Id.ToString())
                .ToArray();
        
        return Results.Ok(result);
    }
}
